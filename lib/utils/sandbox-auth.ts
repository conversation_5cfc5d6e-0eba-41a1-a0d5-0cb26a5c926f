"use client"

/**
 * Sandbox authorization utilities for client-side access control
 */

/**
 * Get the list of authorized sandbox users from environment variables
 */
export function getAuthorizedSandboxUsers(): string[] {
  const authorizedUsers = process.env.NEXT_PUBLIC_SANDBOX_AUTHORIZED_USERS
  if (!authorizedUsers) {
    return []
  }
  
  return authorizedUsers
    .split(',')
    .map(email => email.trim().toLowerCase())
    .filter(email => email.length > 0)
}

/**
 * Check if a user email is authorized for sandbox features
 */
export function isUserAuthorizedForSandbox(userEmail?: string | null): boolean {
  if (!userEmail) {
    return false
  }
  
  const authorizedUsers = getAuthorizedSandboxUsers()
  
  // If no authorized users are configured, deny access
  if (authorizedUsers.length === 0) {
    return false
  }
  
  return authorizedUsers.includes(userEmail.toLowerCase())
}
