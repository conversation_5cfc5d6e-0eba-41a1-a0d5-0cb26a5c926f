import { BaseRealtimeService } from "../base/base.realtime.service"
import { Squad, SquadMember, UserSquad } from "./squad.types"
import { SquadService } from "./squad.service"
import { getAuth } from "firebase/auth"

/**
 * Squad real-time service for Firebase real-time operations
 */
export class SquadRealtimeService {
  private static readonly COLLECTION = "squads"

  /**
   * Subscribe to a squad by ID
   * @param squadId Squad ID
   * @param callback Callback function to handle squad changes
   * @returns Unsubscribe function
   */
  static subscribeToSquad(
    squadId: string,
    callback: (squad: Squad | null, error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToDocument<Squad>(this.COLLECTION, squadId, callback)
  }

  /**
   * Subscribe to squads for a user
   * @param userId User ID
   * @param callback Callback function to handle squads changes
   * @returns Unsubscribe function
   */
  static subscribeToUserSquads(
    userId: string,
    callback: (squads: Squad[], error?: Error) => void
  ): () => void {
    if (!userId || !getAuth().currentUser) {
      // If there's no user ID, don't even try to query Firestore.
      // Immediately return empty data and a no-op unsubscribe function.
      callback([], new Error("No user ID provided"))
      return () => {} // This is a dummy function that does nothing.
    }
    // Subscribe to user's squad subcollection
    return BaseRealtimeService.subscribeToQuery<UserSquad>(
      `users/${userId}/squads`,
      [],
      async (userSquads, error) => {
        if (error) {
          callback([], error)
          return
        }

        if (!getAuth().currentUser) {
          console.warn("User logged out during data fetch; aborting squad details fetch.")
          return
        }

        try {
          // Get squad details for each user squad
          const squads = await SquadService.getSquads(
            userSquads.map((userSquad) => userSquad.squadId)
          )

          // for (const userSquad of userSquads) {
          //   // Get squad document details
          //   const squadDoc = await SquadService.getSquad(userSquad.squadId)
          //   if (squadDoc) {
          //     squads.push(squadDoc)
          //   }
          // }
          callback(squads)
        } catch (err) {
          callback([], err as Error)
        }
      }
    )
  }

  /**
   * Subscribe to squad members
   * @param squadId Squad ID
   * @param callback Callback function to handle members changes
   * @returns Unsubscribe function
   */
  static subscribeToSquadMembers(
    squadId: string,
    callback: (members: SquadMember[], error?: Error) => void
  ): () => void {
    return BaseRealtimeService.subscribeToQuery<SquadMember>(
      `${this.COLLECTION}/${squadId}/members`,
      [],
      callback
    )
  }
}
