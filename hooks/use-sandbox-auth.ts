"use client"

import { useMemo } from "react"
import { useUser } from "@/lib/domains/auth/auth.hooks"
import { isUserAuthorizedForSandbox } from "@/lib/utils/sandbox-auth"

/**
 * Hook to check if the current user is authorized for sandbox features
 */
export function useSandboxAuth() {
  const user = useUser()
  
  const isAuthorized = useMemo(() => {
    return isUserAuthorizedForSandbox(user?.email)
  }, [user?.email])
  
  return {
    isAuthorized,
    userEmail: user?.email,
    isLoading: !user && user !== null, // Loading if user is undefined but not null
  }
}
