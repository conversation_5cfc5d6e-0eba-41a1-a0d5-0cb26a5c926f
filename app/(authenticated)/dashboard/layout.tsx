import { redirect } from "next/navigation"
import { getServerUser } from "@/lib/server/auth"
import { SquadServerHooks } from "@/lib/server/domains/squad"
import { TripServerHooks } from "@/lib/server/domains/trip"
import { UserServerHooks } from "@/lib/server/domains/user"
import { DashboardProvider } from "./components/DashboardProvider"

/**
 * Dashboard Layout - Server-side data fetching with client-side provider
 * Fetches initial data on the server for fast LCP, then provides to all child pages
 */
export default async function DashboardLayout({ children }: { children: React.ReactNode }) {
  // Server-side authentication check
  const serverUser = await getServerUser()

  if (!serverUser) {
    redirect("/login")
  }

  // Parallel server-side data fetching for optimal performance
  const [squadsResult, tripsResult, userResult] = await Promise.allSettled([
    SquadServerHooks.useUserSquads(serverUser.uid),
    TripServerHooks.useUserAttendingTrips(serverUser.uid),
    UserServerHooks.useUser(serverUser.uid),
  ])

  // Extract results with fallbacks
  const initialSquads = squadsResult.status === "fulfilled" ? squadsResult.value : []
  const initialTrips = tripsResult.status === "fulfilled" ? tripsResult.value : []
  const initialUser = userResult.status === "fulfilled" ? userResult.value : null

  // Separate trips into upcoming and past
  const now = new Date()
  const upcomingTrips = initialTrips.filter((trip) => {
    if (!trip.endDate) return false
    const endDate = trip.endDate?.toDate ? trip.endDate.toDate() : new Date(trip.endDate as any)
    return endDate >= now
  })
  const pastTrips = initialTrips.filter((trip) => {
    if (!trip.endDate) return false
    const endDate = trip.endDate?.toDate ? trip.endDate.toDate() : new Date(trip.endDate as any)
    return endDate < now
  })

  // Provide server data to all child pages through context
  return (
    <DashboardProvider
      serverUser={serverUser}
      initialUser={initialUser}
      initialSquads={initialSquads}
      initialUpcomingTrips={upcomingTrips}
      initialPastTrips={pastTrips}
    >
      {children}
    </DashboardProvider>
  )
}
