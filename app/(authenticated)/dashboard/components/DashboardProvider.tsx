"use client"

import { create<PERSON>ontex<PERSON>, use<PERSON><PERSON><PERSON><PERSON>, use<PERSON>ffe<PERSON>, ReactNode } from "react"
import { usePathname } from "next/navigation"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { useUserStore } from "@/lib/domains/user/user.store"
import { useSquadStore } from "@/lib/domains/squad/squad.store"
import { useTripStore } from "@/lib/domains/trip/trip.store"
import type { ServerUser } from "@/lib/server/auth"
import type { Squad } from "@/lib/domains/squad/squad.types"
import type { Trip } from "@/lib/domains/trip/trip.types"
import type { User } from "@/lib/domains/user/user.types"
import Link from "next/link"

interface DashboardContextType {
  serverUser: ServerUser
  initialUser: User | null
  initialSquads: Squad[]
  initialUpcomingTrips: Trip[]
  initialPastTrips: Trip[]
}

const DashboardContext = createContext<DashboardContextType | null>(null)

export function useDashboardContext() {
  const context = useContext(DashboardContext)
  if (!context) {
    throw new Error("useDashboardContext must be used within a DashboardProvider")
  }
  return context
}

interface DashboardProviderProps {
  serverUser: ServerUser
  initialUser: User | null
  initialSquads: Squad[]
  initialUpcomingTrips: Trip[]
  initialPastTrips: Trip[]
  children: ReactNode
}

export function DashboardProvider({
  serverUser,
  initialUser,
  initialSquads,
  initialUpcomingTrips,
  initialPastTrips,
  children,
}: DashboardProviderProps) {
  const pathname = usePathname()

  // Initialize stores with server data
  useEffect(() => {
    // Initialize user store with server data
    if (initialUser) {
      useUserStore.setState({ user: initialUser })
    }

    // Initialize squad store with server data
    useSquadStore.setState({ squads: initialSquads })

    // Initialize trip store with server data
    const allInitialTrips = [...initialUpcomingTrips, ...initialPastTrips]
    useTripStore.setState({ trips: allInitialTrips })
  }, [])

  // Get current user from store (will be initialized with server data)
  const user = useUserStore((state) => state.user)
  const displayUser = user || initialUser

  // Determine current tab based on pathname
  const getCurrentTab = () => {
    if (pathname.includes("/upcoming-trips")) return "upcoming-trips"
    if (pathname.includes("/my-squads")) return "my-squads"
    if (pathname.includes("/past-trips")) return "past-trips"
    return "upcoming-trips" // default
  }

  const contextValue: DashboardContextType = {
    serverUser,
    initialUser,
    initialSquads,
    initialUpcomingTrips,
    initialPastTrips,
  }

  return (
    <DashboardContext.Provider value={contextValue}>
      <div className="p-6">
        <div className="mb-6">
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back,{" "}
            {displayUser?.displayName || displayUser?.name || serverUser.displayName || "Friend"}!
          </p>
        </div>

        <Tabs value={getCurrentTab()} className="space-y-4">
          <TabsList className="w-full">
            <TabsTrigger value="upcoming-trips" className="flex-1 md:flex-initial" asChild>
              <Link href="/dashboard/upcoming-trips">Upcoming Trips</Link>
            </TabsTrigger>
            <TabsTrigger value="my-squads" className="flex-1 md:flex-initial" asChild>
              <Link href="/dashboard/my-squads">My Squads</Link>
            </TabsTrigger>
            <TabsTrigger value="past-trips" className="flex-1 md:flex-initial" asChild>
              <Link href="/dashboard/past-trips">Past Trips</Link>
            </TabsTrigger>
          </TabsList>

          <div className="mt-4">{children}</div>
        </Tabs>
      </div>
    </DashboardContext.Provider>
  )
}
