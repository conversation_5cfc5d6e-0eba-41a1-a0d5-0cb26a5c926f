"use client"

import { Suspense } from "react"
import { useRealtimeUserSquads } from "@/lib/domains/squad/squad.realtime.hooks"
import { useRealtimeUserAllTrips } from "@/lib/domains/trip/trip.realtime.hooks"
import { useDashboardContext } from "../components/DashboardProvider"
import { PastTripsContent } from "./components/PastTripsContent"

// Loading skeleton component
const PastTripsSkeleton = () => (
  <div className="space-y-4">
    <div className="h-6 w-32 bg-muted animate-pulse rounded" />
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {[...Array(3)].map((_, i) => (
        <div key={i} className="h-64 bg-muted animate-pulse rounded-lg" />
      ))}
    </div>
  </div>
)

export default function PastTripsPage() {
  const { initialSquads, initialPastTrips } = useDashboardContext()

  // Get real-time updates (these will start with server data and then update)
  const { squads } = useRealtimeUserSquads(initialSquads)
  const { pastTrips, loading: tripsLoading } = useRealtimeUserAllTrips(
    [], // No upcoming trips needed for this page
    initialPastTrips
  )

  // Use server data immediately, no loading state needed for initial render
  const displaySquads = squads.length > 0 ? squads : initialSquads
  const displayPastTrips = pastTrips.length > 0 ? pastTrips : initialPastTrips

  return (
    <Suspense fallback={<PastTripsSkeleton />}>
      <PastTripsContent
        squads={displaySquads}
        pastTrips={displayPastTrips}
        loading={tripsLoading && displayPastTrips.length === 0}
      />
    </Suspense>
  )
}
